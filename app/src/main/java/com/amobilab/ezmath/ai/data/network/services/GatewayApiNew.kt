package com.amobilab.ezmath.ai.data.network.services

import amobi.module.common.CommApplication
import amobi.module.common.configs.PrefAssist
import amobi.module.common.utils.MixedUtils
import amobi.module.common.utils.debugLog
import android.content.Context
import android.graphics.Bitmap
import android.util.Base64
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.models.Chat
import com.amobilab.ezmath.ai.data.network.models.ChatResponse
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.presentation.common.shared_values.BotType
import com.amobilab.ezmath.ai.presentation.common.shared_values.ChatQuestionMode
import com.amobilab.ezmath.ai.presentation.common.shared_values.ModelAiMode
import com.amobilab.ezmath.ai.utils.AppCheckUtils
import com.amobilab.ezmath.ai.values.Const
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.takeWhile
import kotlinx.coroutines.withContext
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONArray
import org.json.JSONObject
import java.io.ByteArrayOutputStream
import java.io.IOException
import java.util.concurrent.TimeUnit

class GatewayApiNew {
    companion object {
        private const val END_POINT = "http://192.168.1.12:8787/v1/"
        private const val CHAT_COMPLETIONS_ENDPOINT = "chat/completions"
        
        private fun getModelName(): String {
            return if (PrefAssist.getString(PrefConst.MODEL_AI) == ModelAiMode.GPT.name) {
                Const.AiModelName.GPT
            } else {
                Const.AiModelName.GEMINI
            }
        }
        
        private fun getPortkeyProvider(): String {
            return if (PrefAssist.getString(PrefConst.MODEL_AI) == ModelAiMode.GPT.name) "openai"
            else "gemini"
        }
    }

    private val isCollecting = MutableStateFlow(true)
    private val client = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(60, TimeUnit.SECONDS)
        .writeTimeout(60, TimeUnit.SECONDS)
        .build()

    fun stopCollecting() {
        isCollecting.value = false
    }

    suspend fun getResponsesNoStream(prompts: String, bitmap: Bitmap? = null, mode: ChatQuestionMode): ChatResponse {
        debugLog("AI Get Response: $mode")

        val systemInstruction = when (mode) {
            ChatQuestionMode.Translate ->
                CommApplication.appContext.getString(mode.instructionId, PrefAssist.getString(PrefConst.TRANSLATE_TARGET))
            else -> CommApplication.appContext.getString(mode.instructionId)
        }

        val appCheckToken = AppCheckUtils.requestTokenSuspend() ?: run {
            debugLog("Failed to get App Check token")
            return ChatResponse(
                chat = Chat(
                    prompt = "",
                    bitmap = null,
                    isFromUser = false,
                    isError = true,
                    botType = BotType.BOT_GPT
                ),
                inputTokenCount = 0,
                outputTokenCount = 0,
                totalTokenCount = 0
            )
        }

        return try {
            val requestBody = createChatCompletionRequest(
                systemInstruction = systemInstruction,
                userPrompt = prompts,
                bitmap = bitmap,
                stream = false
            )

            val request = Request.Builder()
                .url(END_POINT + CHAT_COMPLETIONS_ENDPOINT)
                .post(requestBody)
                .addHeader("x-portkey-provider", "openai")
                .addHeader("x-gemini-model-id", getModelName())
                .addHeader("x-provider-bacha", getPortkeyProvider())
                .addHeader("Content-Type", "application/json")
                .addHeader("X-Firebase-AppCheck", appCheckToken)
                .addHeader("Authorization", "Bearer sk-xxx")
                .build()

            debugLog("Request headers: ${request.headers}")

            val response = withContext(Dispatchers.IO) {
                client.newCall(request).execute()
            }

            if (response.isSuccessful) {
                val responseBody = response.body?.string()
                debugLog("Response: $responseBody")
                
                val jsonResponse = JSONObject(responseBody ?: "")
                val choices = jsonResponse.getJSONArray("choices")
                val content = if (choices.length() > 0) {
                    choices.getJSONObject(0).getJSONObject("message").getString("content")
                } else ""

                val usage = jsonResponse.optJSONObject("usage")
                val promptTokens = usage?.optInt("prompt_tokens") ?: 0
                val completionTokens = usage?.optInt("completion_tokens") ?: 0
                val totalTokens = usage?.optInt("total_tokens") ?: 0

                ChatResponse(
                    chat = Chat(
                        prompt = content,
                        bitmap = null,
                        isFromUser = false,
                        isError = false,
                        botType = BotType.BOT_GPT
                    ),
                    inputTokenCount = promptTokens,
                    outputTokenCount = completionTokens,
                    totalTokenCount = totalTokens
                )
            } else {
                debugLog("HTTP Error: ${response.code} - ${response.message}")
                ChatResponse(
                    chat = Chat(
                        prompt = "",
                        bitmap = null,
                        isFromUser = false,
                        isError = true,
                        botType = BotType.BOT_GPT
                    ),
                    inputTokenCount = 0,
                    outputTokenCount = 0,
                    totalTokenCount = 0
                )
            }
        } catch (e: Exception) {
            debugLog("Error: ${e.message}")
            ChatResponse(
                chat = Chat(
                    prompt = "",
                    bitmap = null,
                    isFromUser = false,
                    isError = true,
                    botType = BotType.BOT_GPT
                ),
                inputTokenCount = 0,
                outputTokenCount = 0,
                totalTokenCount = 0
            )
        }
    }

    fun getResponseWithImage(
        context: Context,
        prompt: String,
        bitmap: Bitmap,
        mode: ChatQuestionMode
    ): Flow<ChatResponse> = flow {
        debugLog("AI Get Response: $mode")

        val systemInstruction = when (mode) {
            ChatQuestionMode.Translate ->
                CommApplication.appContext.getString(mode.instructionId, PrefAssist.getString(PrefConst.TRANSLATE_TARGET))
            else -> CommApplication.appContext.getString(mode.instructionId)
        }

        val appCheckToken = AppCheckUtils.requestTokenSuspend() ?: run {
            debugLog("Failed to get App Check token")
            withContext(Dispatchers.Main) {
                MixedUtils.showToast(context, R.string.errors_please_try_again)
            }
            return@flow
        }

        var isSentError = true
        try {
            val promptImage = when (mode) {
                ChatQuestionMode.Translate ->
                    context.getString(mode.promptImageId, PrefAssist.getString(PrefConst.TRANSLATE_TARGET))
                else -> context.getString(mode.promptImageId)
            }

            val requestBody = createChatCompletionRequestWithImage(
                systemInstruction = systemInstruction,
                userPrompt = "$promptImage: $prompt",
                bitmap = bitmap,
                stream = true
            )

            val request = Request.Builder()
                .url(END_POINT + CHAT_COMPLETIONS_ENDPOINT)
                .post(requestBody)
                .addHeader("x-portkey-provider", "openai")
                .addHeader("x-gemini-model-id", getModelName())
                .addHeader("x-provider-bacha", getPortkeyProvider())
                .addHeader("Content-Type", "application/json")
                .addHeader("X-Firebase-AppCheck", appCheckToken)
                .addHeader("Authorization", "Bearer sk-xxx")
                .build()

            withContext(Dispatchers.IO) {
                val response = client.newCall(request).execute()
                if (response.isSuccessful) {
                    response.body?.source()?.let { source ->
                        while (!source.exhausted()) {
                            val line = source.readUtf8Line()
                            if (line?.startsWith("data: ") == true) {
                                val data = line.substring(6)
                                if (data != "[DONE]") {
                                    try {
                                        val jsonData = JSONObject(data)
                                        val choices = jsonData.getJSONArray("choices")
                                        if (choices.length() > 0) {
                                            val delta = choices.getJSONObject(0).optJSONObject("delta")
                                            val content = delta?.optString("content")
                                            
                                            if (content != null && content.isNotEmpty()) {
                                                isSentError = false
                                                emit(
                                                    ChatResponse(
                                                        chat = Chat(
                                                            prompt = content,
                                                            bitmap = null,
                                                            isFromUser = false,
                                                            isError = false,
                                                            botType = BotType.BOT_GPT
                                                        ),
                                                        inputTokenCount = 0,
                                                        outputTokenCount = 0,
                                                        totalTokenCount = 0
                                                    )
                                                )
                                            }
                                        }
                                    } catch (e: Exception) {
                                        debugLog("Parse error: ${e.message}")
                                    }
                                }
                            }
                        }
                    }
                } else {
                    debugLog("HTTP Error: ${response.code} - ${response.message}")
                    throw IOException("HTTP ${response.code}")
                }
            }

        } catch (e: Exception) {
            debugLog("Response error: ${e.message}")
            emit(
                ChatResponse(
                    chat = Chat(
                        prompt = if (isSentError) context.getString(R.string.errors_please_try_again) else "",
                        bitmap = null,
                        isFromUser = false,
                        isError = true,
                        botType = BotType.BOT_GPT
                    ),
                    inputTokenCount = 0,
                    outputTokenCount = 0,
                    totalTokenCount = 0
                )
            )
            withContext(Dispatchers.Main) {
                MixedUtils.showToast(context, R.string.errors_please_try_again)
            }
        }
    }.catch { e ->
        debugLog("Response error: ${e.message}")
        emit(
            ChatResponse(
                chat = Chat(
                    prompt = context.getString(R.string.errors_please_try_again),
                    bitmap = null,
                    isFromUser = false,
                    isError = true,
                    botType = BotType.BOT_GPT
                ),
                inputTokenCount = 0,
                outputTokenCount = 0,
                totalTokenCount = 0
            )
        )
        isCollecting.value = false
        withContext(Dispatchers.Main) {
            MixedUtils.showToast(context, R.string.errors_please_try_again)
        }
    }

    fun getResponses(context: Context, listChat: MutableList<Chat>, mode: ChatQuestionMode): Flow<ChatResponse?> = flow {
        debugLog("AI Get Response: $mode")

        val appCheckToken = AppCheckUtils.requestTokenSuspend() ?: run {
            debugLog("Failed to get App Check token")
            withContext(Dispatchers.Main) {
                MixedUtils.showToast(context, R.string.errors_please_try_again)
            }
            return@flow
        }

        isCollecting.value = true
        val systemInstruction = when (mode) {
            ChatQuestionMode.Translate ->
                CommApplication.appContext.getString(mode.instructionId, PrefAssist.getString(PrefConst.TRANSLATE_TARGET))
            else -> CommApplication.appContext.getString(mode.instructionId) + ".\n" + "Returns the formula in KaTeX-compatible format"
        }

        var isSentError = true
        try {
            val requestBody = createChatCompletionRequestWithHistory(
                systemInstruction = systemInstruction,
                chatHistory = listChat.take(6).reversed(),
                stream = true
            )

            val request = Request.Builder()
                .url(END_POINT + CHAT_COMPLETIONS_ENDPOINT)
                .post(requestBody)
                .addHeader("x-portkey-provider", "openai")
                .addHeader("x-gemini-model-id", getModelName())
                .addHeader("x-provider-bacha", getPortkeyProvider())
                .addHeader("Content-Type", "application/json")
                .addHeader("X-Firebase-AppCheck", appCheckToken)
                .addHeader("Authorization", "Bearer sk-xxx")
                .build()

            withContext(Dispatchers.IO) {
                val response = client.newCall(request).execute()
                if (response.isSuccessful) {
                    response.body?.source()?.let { source ->
                        while (!source.exhausted() && isCollecting.value) {
                            val line = source.readUtf8Line()
                            if (line?.startsWith("data: ") == true) {
                                val data = line.substring(6)
                                if (data != "[DONE]") {
                                    try {
                                        val jsonData = JSONObject(data)
                                        val choices = jsonData.getJSONArray("choices")
                                        if (choices.length() > 0) {
                                            val delta = choices.getJSONObject(0).optJSONObject("delta")
                                            val content = delta?.optString("content")

                                            if (content != null && content.isNotEmpty()) {
                                                isSentError = false
                                            }

                                            val usage = jsonData.optJSONObject("usage")
                                            val promptTokens = usage?.optInt("prompt_tokens") ?: 0
                                            val completionTokens = usage?.optInt("completion_tokens") ?: 0
                                            val totalTokens = usage?.optInt("total_tokens") ?: 0

                                            emit(
                                                ChatResponse(
                                                    chat = Chat(
                                                        prompt = content ?: "",
                                                        bitmap = null,
                                                        isFromUser = false,
                                                        isError = false,
                                                        botType = BotType.BOT_GPT
                                                    ),
                                                    inputTokenCount = promptTokens,
                                                    outputTokenCount = completionTokens,
                                                    totalTokenCount = totalTokens
                                                )
                                            )

                                            debugLog("response2 inputTokenCount $promptTokens outputTokenCount $completionTokens totalTokenCount $totalTokens")
                                        }
                                    } catch (e: Exception) {
                                        debugLog("Parse error: ${e.message}")
                                    }
                                }
                            }
                        }
                    }
                } else {
                    debugLog("HTTP Error: ${response.code} - ${response.message}")
                    throw IOException("HTTP ${response.code}")
                }
            }
        } catch (e: Exception) {
            debugLog("Response error stream: ${e.message}")
            try {
                emit(
                    ChatResponse(
                        chat = Chat(
                            prompt = if (isSentError) context.getString(R.string.errors_please_try_again) else "",
                            bitmap = null,
                            isFromUser = false,
                            isError = true,
                            botType = BotType.BOT_GPT
                        ),
                        inputTokenCount = 0,
                        outputTokenCount = 0,
                        totalTokenCount = 0
                    )
                )
            } catch (e: Exception) {
                debugLog("Response error stream: ${e.message}")
            }
            isCollecting.value = false
            withContext(Dispatchers.Main) {
                MixedUtils.showToast(context, R.string.errors_please_try_again)
            }
        }
    }.catch { e ->
        debugLog("Response error stream: ${e.message}")
        try {
            emit(
                ChatResponse(
                    chat = Chat(
                        prompt = context.getString(R.string.errors_please_try_again),
                        bitmap = null,
                        isFromUser = false,
                        isError = true,
                        botType = BotType.BOT_GPT
                    ),
                    inputTokenCount = 0,
                    outputTokenCount = 0,
                    totalTokenCount = 0
                )
            )
        } catch (e: Exception) {
            debugLog("Response error stream: ${e.message}")
        }
        isCollecting.value = false
        withContext(Dispatchers.Main) {
            MixedUtils.showToast(context, R.string.errors_please_try_again)
        }
    }

    private fun createChatCompletionRequest(
        systemInstruction: String,
        userPrompt: String,
        bitmap: Bitmap? = null,
        stream: Boolean = false
    ): RequestBody {
        val jsonObject = JSONObject().apply {
            put("model", getModelName())
            put("stream", stream)
            if (!stream) {
                put("max_tokens", Const.MAX_OUTPUT_TOKENS)
            } else {
                put("max_tokens", Const.MAX_OUTPUT_TOKENS)
                put("stream_options", JSONObject().put("include_usage", true))
            }

            val messages = JSONArray().apply {
                // System message
                put(JSONObject().apply {
                    put("role", "system")
                    put("content", systemInstruction)
                })

                // User message
                if (bitmap != null) {
                    put(JSONObject().apply {
                        put("role", "user")
                        put("content", JSONArray().apply {
                            put(JSONObject().apply {
                                put("type", "text")
                                put("text", userPrompt)
                            })
                            put(JSONObject().apply {
                                put("type", "image_url")
                                put("image_url", JSONObject().apply {
                                    put("url", "data:image/png;base64," + encodeToBase64(bitmap))
                                    put("detail", "low")
                                })
                            })
                        })
                    })
                } else {
                    put(JSONObject().apply {
                        put("role", "user")
                        put("content", userPrompt)
                    })
                }
            }
            put("messages", messages)
        }

        return jsonObject.toString().toRequestBody("application/json".toMediaType())
    }

    private fun createChatCompletionRequestWithImage(
        systemInstruction: String,
        userPrompt: String,
        bitmap: Bitmap,
        stream: Boolean = true
    ): RequestBody {
        val jsonObject = JSONObject().apply {
            put("model", getModelName())
            put("stream", stream)
            put("max_tokens", Const.MAX_OUTPUT_TOKENS)
            if (stream) {
                put("stream_options", JSONObject().put("include_usage", true))
            }

            val messages = JSONArray().apply {
                // System message
                put(JSONObject().apply {
                    put("role", "system")
                    put("content", systemInstruction)
                })

                // User message with image
                put(JSONObject().apply {
                    put("role", "user")
                    put("content", JSONArray().apply {
                        put(JSONObject().apply {
                            put("type", "text")
                            put("text", userPrompt)
                        })
                        put(JSONObject().apply {
                            put("type", "image_url")
                            put("image_url", JSONObject().apply {
                                put("url", "data:image/png;base64," + encodeToBase64(bitmap))
                                put("detail", "low")
                            })
                        })
                    })
                })
            }
            put("messages", messages)
        }

        return jsonObject.toString().toRequestBody("application/json".toMediaType())
    }

    private fun createChatCompletionRequestWithHistory(
        systemInstruction: String,
        chatHistory: List<Chat>,
        stream: Boolean = true
    ): RequestBody {
        val jsonObject = JSONObject().apply {
            put("model", getModelName())
            put("stream", stream)
            put("max_tokens", Const.MAX_OUTPUT_TOKENS)
            if (stream) {
                put("stream_options", JSONObject().put("include_usage", true))
            }

            val messages = JSONArray().apply {
                // System message
                put(JSONObject().apply {
                    put("role", "system")
                    put("content", systemInstruction)
                })

                // Chat history
                chatHistory.forEach { chat ->
                    if (chat.isFromUser) {
                        if (chat.bitmap != null) {
                            put(JSONObject().apply {
                                put("role", "user")
                                put("content", JSONArray().apply {
                                    put(JSONObject().apply {
                                        put("type", "text")
                                        put("text", chat.prompt)
                                    })
                                    put(JSONObject().apply {
                                        put("type", "image_url")
                                        put("image_url", JSONObject().apply {
                                            put("url", "data:image/png;base64," + encodeToBase64(chat.bitmap))
                                            put("detail", "low")
                                        })
                                    })
                                })
                            })
                        } else {
                            put(JSONObject().apply {
                                put("role", "user")
                                put("content", chat.prompt)
                            })
                        }
                    } else {
                        put(JSONObject().apply {
                            put("role", "assistant")
                            put("content", chat.prompt)
                        })
                    }
                }
            }
            put("messages", messages)
        }

        debugLog("Request JSON: ${jsonObject.toString()}")
        return jsonObject.toString().toRequestBody("application/json".toMediaType())
    }

    private fun encodeToBase64(bitmap: Bitmap): String {
        val byteArrayOutputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, byteArrayOutputStream)
        return Base64.encodeToString(byteArrayOutputStream.toByteArray(), Base64.NO_WRAP)
    }
}
